
import type React from "react"

import { <PERSON> } from "react-router-dom"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowRight, Check } from "lucide-react"
import { useState } from "react"
// import './index.css'


export default function SignUpPage() {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
  })

  const passwordRequirements = [
    { text: "Be at least 8 characters long", met: formData.password.length >= 8 },
    { text: "Contain at least 2 numbers", met: (formData.password.match(/\d/g) || []).length >= 2 },
    { text: "Contain at least one special character", met: /[!@#$%^&*(),.?":{}|<>]/.test(formData.password) },
    {
      text: "Enter and Confirm passwords should match",
      met: formData.password === formData.confirmPassword && formData.confirmPassword !== "",
    },
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    })
  }

  return (
    <div className="min-h-screen flex bg-gray-50">
      {/* Left Side - Form */}
      <div className="w-1/2 bg-gray-50 flex flex-col">
        {/* Logo at top left */}
        <div className="p-8 pb-0">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg"></div>
            <span className="text-xl font-semibold text-gray-900">Navi</span>
          </div>
        </div>

        {/* Form Content positioned to the right */}
        <div className="flex-1 flex items-center justify-end pr-16">
          <div className="w-full max-w-sm space-y-6">
            {/* Create Account Header */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-gray-900">Create Your Account</h1>
              <p className="text-gray-600">{"Let's get you set up in just a minute"}</p>
            </div>

            {/* Form */}
            <form className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-900">
                  Email Address
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter Your Email Address"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="h-11 px-3 border border-gray-300 rounded-md placeholder:text-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-900">
                  Enter a Password
                </Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="Enter your Password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="h-11 px-3 border border-gray-300 rounded-md placeholder:text-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="confirmPassword" className="text-sm font-medium text-gray-900">
                  Re-enter the Password
                </Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="Enter same password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="h-11 px-3 border border-gray-300 rounded-md placeholder:text-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
              </div>

              {/* Password Requirements */}
              <div className="space-y-3">
                <p className="text-sm font-medium text-gray-900">Passwords must :</p>
                <div className="space-y-2">
                  {passwordRequirements.map((requirement, index) => (
                    <div key={index} className="flex items-center space-x-2">
                      <Check className={`w-4 h-4 ${requirement.met ? "text-green-500" : "text-gray-300"}`} />
                      <span className={`text-sm ${requirement.met ? "text-gray-900" : "text-gray-600"}`}>
                        {requirement.text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full h-11 bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 rounded-md font-medium flex items-center justify-center gap-2 mt-6"
              >
                Create Account
                <ArrowRight className="w-4 h-4" />
              </Button>
            </form>

            {/* Bottom Link */}
            <div className="text-center pt-4">
              <span className="text-gray-600">Already have a Square account? </span>
              <Link to="/" className="text-blue-600 hover:text-blue-500 font-medium underline">
                Sign in
              </Link>
              <span className="text-gray-600">.</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Rounded Gradient Card */}
      <div className="w-1/2 bg-gray-50 flex items-center justify-center p-8">
        <div className="w-full h-5/6 bg-gradient-to-br from-blue-400 via-blue-500 to-purple-600 rounded-3xl"></div>
      </div>
    </div>
  )
}
