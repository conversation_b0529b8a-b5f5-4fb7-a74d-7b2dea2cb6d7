import { Link } from "react-router-dom"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { ArrowRight } from "lucide-react"
// import './index.css'


export default function SignInPage() {
  return (
    <div className="min-h-screen flex bg-gray-50">
      {/* Left Side - Form */}
      <div className="w-1/2 bg-gray-50 flex flex-col">
        {/* Logo at top left */}
        <div className="p-8 pb-0">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg"></div>
            <span className="text-xl font-semibold text-gray-900">Navi</span>
          </div>
        </div>

        {/* Form Content positioned to the right */}
        <div className="flex-1 flex items-center justify-end pr-16">
          <div className="w-full max-w-sm space-y-6">
            {/* Sign In Header */}
            <div className="space-y-2">
              <h1 className="text-3xl font-bold text-gray-900">Sign In</h1>
              <p className="text-gray-600">Log in to continue managing your motel</p>
            </div>

            {/* Form */}
            <form className="space-y-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-900">
                  Email Address or Phone
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter Your Email Address or Phone Number"
                  className="h-11 px-3 border border-gray-300 rounded-md placeholder:text-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="password" className="text-sm font-medium text-gray-900">
                  Password
                </Label>
                <Input
                  id="password"
                  type="password"
                  placeholder="Enter Your Password"
                  className="h-11 px-3 border border-gray-300 rounded-md placeholder:text-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="text-right">
                <Link to="#" className="text-sm text-blue-600 hover:text-blue-500">
                  Forgot Password ?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full h-11 bg-white border border-gray-300 text-gray-900 hover:bg-gray-50 rounded-md font-medium flex items-center justify-center gap-2"
              >
                Sign In
                <ArrowRight className="w-4 h-4" />
              </Button>
            </form>

            {/* Bottom Link */}
            <div className="text-center pt-4">
              <span className="text-gray-600">{"Don't have an account? "}</span>
              <Link to="/signup" className="text-blue-600 hover:text-blue-500 font-medium">
                Create One
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Rounded Gradient Card */}
      <div className="w-1/2 bg-gray-50 flex items-center justify-center p-8">
        <div className="w-full h-5/6 bg-gradient-to-br from-blue-400 via-blue-500 to-purple-600 rounded-3xl"></div>
      </div>
    </div>
  )
}
